# S3 Authentication Server

A Python-based authentication server that provides secured access to AWS S3 buckets. The server uses JWT for authentication and can proxy S3 operations such as listing, downloading, uploading, and deleting files.

## Features

- JWT-based authentication via `/auth/token` endpoint.
- API key validation for token generation.
- **Flexible authentication for S3 endpoints (JWT or trusted X-API-Key)**.
- Support for multiple S3 buckets.
- IMDSv2 for EC2 instance authentication with AWS.
- HAProxy compatible.
- Comprehensive test suite.

## Architecture

1.  **Authentication Flow**:

    The server supports two main authentication methods for accessing S3 operations:

    *   **Standard JWT Flow (Recommended for most clients):**
        1.  Clients send their API key in the `X-API-Key` header to the `POST /auth/token` endpoint.
        2.  The server validates the API key and, if valid, issues a JWT (JSON Web Token).
        3.  The client uses this JWT in the `Authorization: Bearer <token>` header for all subsequent S3 operations.

    *   **Direct API Key Access (For trusted internal clients, e.g., Grafana):**
        1.  Trusted internal clients can directly access S3 operation endpoints by providing their API key in the `X-API-Key` header.
        2.  This method bypasses the `/auth/token` step.
        3.  To enable this, the specific API key must be configured with `"trusted_for_direct_access": True` in the server's `app/config.py` file (see Configuration).
        4.  If an `Authorization: Bearer <token>` header is present, it will be prioritized over the `X-API-Key` for these endpoints.

    For both flows, the server uses the EC2 instance role (via IMDSv2) to interact with AWS S3.

2. **API Endpoints**:

- `POST /auth/token` — Generate JWT token using API key.
- `GET /health` — Health check endpoint.
- `GET /s3/buckets/{bucket_name}/files` — List files in a bucket.
- `GET /s3/buckets/{bucket_name}/file?object_key={object_key}` — Download a file.
- `POST /s3/buckets/{bucket_name}/file?object_key={object_key}` — Upload a file.
- `DELETE /s3/buckets/{bucket_name}/file?object_key={object_key}` — Delete a file.

## Setup and Configuration

### Environment Variables

```
JWT_SECRET_KEY=your-secret-key
JWT_EXPIRE_MINUTES=60
AWS_REGION=ap-southeast-2
DEFAULT_API_KEY=your-api-key # Required - For general client JWT generation
GRAFANA_API_KEY=your-grafana-specific-api-key # Optional - Example for a trusted internal client
DEBUG=False
HOST=0.0.0.0
PORT=8000
```

### API Key Configuration (`app/config.py`)

API keys are defined in `authentication_server/app/config.py` within the `API_KEYS` dictionary. Each key can have associated information like `client_id` and `scopes`.

To enable direct API key access for a trusted internal client (like Grafana) for S3 endpoints, you must add `"trusted_for_direct_access": True` to its configuration.

Example `API_KEYS` structure in `app/config.py`:
```python
# Load API keys from environment variables - HARDCODED DEFAULTS REMOVED FOR SECURITY
DEFAULT_API_KEY = os.getenv("DEFAULT_API_KEY")
GRAFANA_API_KEY = os.getenv("GRAFANA_API_KEY")

if not DEFAULT_API_KEY:
    raise ValueError("DEFAULT_API_KEY environment variable must be set")

API_KEYS: Dict[str, Dict] = {
    DEFAULT_API_KEY: {
        "client_id": "default_client",
        "scopes": ["read", "write"]
        # This key is NOT trusted for direct access by default
    }
}

if GRAFANA_API_KEY:
    API_KEYS[GRAFANA_API_KEY] = {
        "client_id": "grafana_service",
        "scopes": ["s3:read", "s3:list"], # Example scopes Grafana might need
        "trusted_for_direct_access": True  # Allows direct X-API-Key auth for S3 ops
    }
```
**Important**: The `DEFAULT_API_KEY` environment variable is required and must be set to a strong, unique value. The `GRAFANA_API_KEY` is optional but if provided, should also be a strong, unique value.

### IAM Role Requirements

The EC2 instance running this server must have an IAM role with appropriate S3 permissions:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "s3:GetObject",
                "s3:PutObject",
                "s3:DeleteObject",
                "s3:ListBucket"
            ],
            "Resource": [
                "arn:aws:s3:::*"
            ]
        }
    ]
}
```

### Installation

1. Clone the repository
2. Install requirements:
    ```
    pip install -r requirements.txt
    ```
3. Install the package in development mode (for testing):
    ```
    pip install -e .
    ```
4. Run the server:
    ```
    python authentication_server/run.py
    ```

**Note**: The repository contains an empty `package.json` file which can be ignored as this is a Python-only project.

## Testing

The project includes a comprehensive test suite using pytest. The test suite includes unit tests for authentication endpoints, JWT token handling, S3 operations, and error cases.

### Running Tests

To run the test suite:

```bash
pytest authentication_server/tests/
```

For verbose output:

```bash
pytest authentication_server/tests/ -v
```

### Test Categories

- **Authentication tests**: Verifies token generation, API key validation, and health endpoints
- **Authorization tests**: Tests JWT token validation, expiration, and error handling  
- **S3 Service tests**: Tests S3 operations (list, download, upload, delete) with mocked responses
- **End-to-end tests**: Tests the full request flow from authentication to S3 operations

The tests use mocking to avoid making actual AWS calls, making them fast, reliable, and suitable for CI/CD pipelines.

## Security Considerations and Improvements

While the authentication server provides basic security features, there are several improvements that should be considered before using it in a production environment:

### Security Enhancements

-   **Required API Keys**: Ensure the `DEFAULT_API_KEY` environment variable is set to a strong, unique value. If using Grafana integration, set `GRAFANA_API_KEY` to a strong, unique value as well.
-   **Implement Token Scopes**: Currently, all tokens have read/write access; implement token scope verification to restrict operations. Ensure scopes are also checked for direct API key access.
-   **Rate Limiting**: Add rate limiting on token generation and potentially on direct API key access to prevent brute force attacks.
-   **Input Validation**: Strengthen file path validation to prevent path traversal attacks.
-   **Content Type Security**: Improve MIME type detection and validation for downloaded and uploaded files.
-   **File Size Limits**: Implement configurable file size limits for uploads.
-   **Secure Headers**: Add security headers such as X-Content-Type-Options, X-Frame-Options, etc.

### Performance Improvements

- **Service Reuse**: Create S3Service once and reuse it across requests
- **Connection Pooling**: Configure boto3 to use connection pooling for better performance
- **Pagination Support**: Add pagination for listing files in buckets with many objects
- **Async Operations**: Consider using async S3 operations for better concurrency

### Monitoring and Logging

- **Request IDs**: Add unique request IDs to log entries for better traceability
- **Structured Logging**: Implement structured logging for easier log analysis
- **Metrics**: Add metrics collection for monitoring authentication failures, S3 errors, etc.
- **Audit Logging**: Add comprehensive audit logging for security-sensitive operations

### Additional Test Cases

- Test for invalid bucket name formats
- Test for path traversal attempts
- Test for token scope verification
- Test for large file uploads and pagination

## Usage Examples

### Health Check

```bash
curl -X GET http://your-server:8000/health
```

Response:
```json
{
  "status": "healthy"
}
```

### Getting a JWT Token

(For clients using the standard JWT flow)

```bash
curl -X POST http://your-server:8000/auth/token \
  -H "X-API-Key: your-general-api-key" \
  -H "Content-Type: application/json"
```

Response:
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 3600
}
```

### Listing Files in a Bucket

**Option 1: Using JWT Token**
```bash
curl -X GET http://your-server:8000/s3/buckets/your-bucket-name/files \
  -H "Authorization: Bearer your-jwt-token"
```

**Option 2: Using a Trusted X-API-Key (e.g., for Grafana)**
```bash
curl -X GET http://your-server:8000/s3/buckets/your-bucket-name/files \
  -H "X-API-Key: your-grafana-specific-api-key"
```
*(This requires `your-grafana-specific-api-key` to be configured with `"trusted_for_direct_access": True` in `app/config.py`)*

**With optional prefix filter:**
```bash
curl -X GET "http://your-server:8000/s3/buckets/your-bucket-name/files?prefix=folder/" \
  -H "Authorization: Bearer your-jwt-token"
```

### Downloading a File

**Option 1: Using JWT Token**
```bash
curl -X GET "http://your-server:8000/s3/buckets/your-bucket-name/file?object_key=path/to/file.txt" \
  -H "Authorization: Bearer your-jwt-token" \
  --output file.txt
```

**Option 2: Using a Trusted X-API-Key**
```bash
curl -X GET "http://your-server:8000/s3/buckets/your-bucket-name/file?object_key=path/to/file.txt" \
  -H "X-API-Key: your-grafana-specific-api-key" \
  --output file.txt
```

### Uploading a File

**Option 1: Using JWT Token**
```bash
curl -X POST "http://your-server:8000/s3/buckets/your-bucket-name/file?object_key=path/to/file.txt" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@/path/to/local/file.txt"
```

**Option 2: Using a Trusted X-API-Key**
```bash
curl -X POST "http://your-server:8000/s3/buckets/your-bucket-name/file?object_key=path/to/file.txt" \
  -H "X-API-Key: your-grafana-specific-api-key" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@/path/to/local/file.txt"
```

### Deleting a File

**Option 1: Using JWT Token**
```bash
curl -X DELETE "http://your-server:8000/s3/buckets/your-bucket-name/file?object_key=path/to/file.txt" \
  -H "Authorization: Bearer your-jwt-token"
```

**Option 2: Using a Trusted X-API-Key**
```bash
curl -X DELETE "http://your-server:8000/s3/buckets/your-bucket-name/file?object_key=path/to/file.txt" \
  -H "X-API-Key: your-grafana-specific-api-key"
```

## HAProxy Integration

To configure HAProxy to use this authentication server:

1.  Get a JWT token (standard flow):
    ```haproxy
    # Example: Fetch token and store in a variable (actual implementation may vary)
    # This usually involves an ACL and an http-request rule to call /auth/token
    # and capture the response. HAProxy's capabilities for this are limited
    # and might require Lua scripting for complex token handling.
    # A simpler approach for HAProxy might be to use the direct X-API-Key method
    # if HAProxy itself is considered a trusted client.
    ```

2.  Use token for file operations (standard flow):
    ```haproxy
    # If token is stored in txn.auth_token
    http-request set-header Authorization Bearer %[var(txn.auth_token)]
    ```

3.  **Alternative for HAProxy (if trusted):**
    If HAProxy can be configured with its own trusted API key, it could directly use the `X-API-Key` header for requests to the S3 backend, simplifying the HAProxy configuration.
    ```haproxy
    http-request set-header X-API-Key your-haproxy-trusted-api-key
    ```

**Note**: When proxying requests to S3 file operations, remember that the actual endpoints use query parameters:
- File downloads: `/s3/buckets/{bucket}/file?object_key={key}`
- File uploads: `/s3/buckets/{bucket}/file?object_key={key}`
- File deletions: `/s3/buckets/{bucket}/file?object_key={key}`


Example HAProxy configuration:

```haproxy
backend s3_auth_server
    mode http
    server s3auth 127.0.0.1:8000
    
    # Get JWT token
    http-request set-var(txn.api_key) str(your-api-key)
    http-request set-var(txn.auth_resp) do-resolve(auth-req,auth-resp)
    http-request capture var(txn.auth_resp) len 512
    
    # Parse JWT token from response
    http-request set-var(txn.auth_token) json_query(var(txn.auth_resp), '$.access_token')
    
    # Use token for file operations
    http-request add-header Authorization "Bearer %[var(txn.auth_token)]"
```

## License

MIT 