import os
import secrets
from typing import Dict, List, Optional

# JWT settings
JWT_SECRET_KEY = os.getenv("JWT_SECRET_KEY", secrets.token_hex(32))
JWT_ALGORITHM = "HS256"
JWT_ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("JWT_EXPIRE_MINUTES", "60"))

# AWS settings
AWS_REGION = os.getenv("AWS_REGION", "ap-southeast-2")

# API settings
# Load API keys from environment variables - HARDCODED DEFAULTS REMOVED FOR SECURITY
DEFAULT_API_KEY = os.getenv("DEFAULT_API_KEY")
GRAFANA_API_KEY = os.getenv("GRAFANA_API_KEY")

if not DEFAULT_API_KEY:
    raise ValueError("DEFAULT_API_KEY environment variable must be set")

API_KEYS: Dict[str, Dict] = {
    DEFAULT_API_KEY: {
        "client_id": "default_client",
        "scopes": ["read", "write"]
    }
}

if GRAFANA_API_KEY:
    API_KEYS[GRAFANA_API_KEY] = {
        "client_id": "grafana_service",
        "scopes": ["s3:read", "s3:list"],
        "trusted_for_direct_access": True
    }

# Server settings
DEBUG = os.getenv("DEBUG", "False").lower() in ("true", "1", "t")
HOST = os.getenv("HOST", "0.0.0.0")
PORT = int(os.getenv("PORT", "8000")) 