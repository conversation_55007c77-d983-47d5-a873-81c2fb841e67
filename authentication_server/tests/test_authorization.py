import pytest
from fastapi import HTTPException
from jose import jwt

from authentication_server.app.services.auth_service import create_jwt_token, verify_jwt_token
from authentication_server.app.config import JWT_SECRET_KEY, JWT_ALGORITHM


def test_authorization_header_parsing(test_client, api_key, mock_s3_client, mock_s3_list_objects_response):
    """
    Test that the authorization header is properly parsed
    """
    # Configure the mock
    mock_s3_client.list_objects_v2.return_value = mock_s3_list_objects_response
    
    # Get a token
    auth_response = test_client.post(
        "/auth/token",
        headers={"X-API-Key": api_key}
    )
    token = auth_response.json()["access_token"]
    
    # Call an S3 endpoint with the token
    response = test_client.get(
        "/s3/buckets/test-bucket/files",
        headers={"Authorization": f"Bearer {token}"}
    )
    
    # Should not get a 401/403
    assert response.status_code != 401
    assert response.status_code != 403


def test_missing_authorization_header(test_client):
    """
    Test missing authorization header
    """
    response = test_client.get("/s3/buckets/test-bucket/files")
    assert response.status_code == 401  # Unauthorized for missing header


def test_invalid_authorization_format(test_client):
    """
    Test invalid authorization header format
    """
    response = test_client.get(
        "/s3/buckets/test-bucket/files",
        headers={"Authorization": "InvalidFormat token123"}
    )
    assert response.status_code == 401
    assert "Could not validate credentials" in response.json()["detail"]


def test_invalid_token(test_client):
    """
    Test using an invalid token
    """
    response = test_client.get(
        "/s3/buckets/test-bucket/files",
        headers={"Authorization": "Bearer invalid-token"}
    )
    assert response.status_code == 401
    assert "Could not validate credentials" in response.json()["detail"]


def test_expired_token(test_client):
    """
    Test using an expired token
    """
    # Create an expired token
    import time
    from datetime import datetime, timedelta
    
    expired_payload = {
        "sub": "test",
        "exp": int((datetime.utcnow() - timedelta(minutes=5)).timestamp())
    }
    expired_token = jwt.encode(expired_payload, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)
    
    response = test_client.get(
        "/s3/buckets/test-bucket/files",
        headers={"Authorization": f"Bearer {expired_token}"}
    )
    assert response.status_code == 401 