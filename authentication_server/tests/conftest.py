import pytest
from fastapi.testclient import Test<PERSON>lient
import os
from unittest.mock import patch, MagicMock
from datetime import datetime
import boto3
import io

# Set test environment variables before importing the app
os.environ.setdefault("DEFAULT_API_KEY", "test_api_key_12345")
os.environ.setdefault("JWT_SECRET_KEY", "test_jwt_secret_key")

from authentication_server.app.main import app
from authentication_server.app.config import API_KEYS, JWT_SECRET_KEY


@pytest.fixture
def test_client():
    """
    Create a test client for the FastAPI app
    """
    with TestClient(app) as client:
        yield client


@pytest.fixture
def api_key():
    """
    Get the first API key from the configuration
    """
    return next(iter(API_KEYS.keys()))


@pytest.fixture
def mock_s3_client():
    """
    Create a mocked S3 client for testing
    """
    mock_client = MagicMock()
    with patch("boto3.client", return_value=mock_client):
        yield mock_client


@pytest.fixture
def mock_s3_get_object_response():
    """
    Create a mock response for S3 get_object method
    """
    mock_body = MagicMock()
    mock_body.read.return_value = b"test file content"
    return {
        "Body": mock_body,
        "ContentLength": 17,
        "LastModified": datetime.now()
    }


@pytest.fixture
def mock_s3_list_objects_response():
    """
    Create a mock response for S3 list_objects_v2 method
    """
    return {
        "Contents": [
            {
                "Key": "test_file1.txt",
                "Size": 100,
                "LastModified": datetime.now()
            },
            {
                "Key": "test_file2.json",
                "Size": 200,
                "LastModified": datetime.now()
            }
        ]
    }