import pytest
from unittest.mock import patch, MagicMock
import io
import json
from botocore.exceptions import Client<PERSON>rro<PERSON>


def test_s3_list_files(test_client, api_key, mock_s3_client, mock_s3_list_objects_response):
    """
    Test listing files in an S3 bucket
    """
    # Get an auth token first
    auth_response = test_client.post(
        "/auth/token",
        headers={"X-API-Key": api_key}
    )
    token = auth_response.json()["access_token"]
    
    # Configure the mock
    mock_s3_client.list_objects_v2.return_value = mock_s3_list_objects_response
    
    # Call the list files endpoint
    response = test_client.get(
        "/s3/buckets/test-bucket/files",
        headers={"Authorization": f"Bearer {token}"}
    )
    
    # Check the response
    assert response.status_code == 200
    data = response.json()
    assert data["bucket"] == "test-bucket"
    assert len(data["files"]) == 2
    
    # Verify the S3 client was called with correct parameters
    mock_s3_client.list_objects_v2.assert_called_once_with(
        Bucket="test-bucket",
        Prefix=""
    )


def test_s3_list_files_with_prefix(test_client, api_key, mock_s3_client, mock_s3_list_objects_response):
    """
    Test listing files in an S3 bucket with a prefix
    """
    # Get an auth token first
    auth_response = test_client.post(
        "/auth/token",
        headers={"X-API-Key": api_key}
    )
    token = auth_response.json()["access_token"]
    
    # Configure the mock
    mock_s3_client.list_objects_v2.return_value = mock_s3_list_objects_response
    
    # Call the list files endpoint with a prefix
    response = test_client.get(
        "/s3/buckets/test-bucket/files?prefix=test",
        headers={"Authorization": f"Bearer {token}"}
    )
    
    # Check the response
    assert response.status_code == 200
    
    # Verify the S3 client was called with correct parameters
    mock_s3_client.list_objects_v2.assert_called_once_with(
        Bucket="test-bucket",
        Prefix="test"
    )


def test_s3_download_file(test_client, api_key, mock_s3_client, mock_s3_get_object_response):
    """
    Test downloading a file from S3
    """
    # Get an auth token first
    auth_response = test_client.post(
        "/auth/token",
        headers={"X-API-Key": api_key}
    )
    token = auth_response.json()["access_token"]
    
    # Configure the mock
    mock_s3_client.get_object.return_value = mock_s3_get_object_response
    
    # Call the download endpoint
    response = test_client.get(
        "/s3/buckets/test-bucket/file?object_key=test_file.txt",  # Changed URL
        headers={"Authorization": f"Bearer {token}"}
    )
    
    # Check the response
    assert response.status_code == 200
    assert response.content == b"test file content"
    
    # Verify the S3 client was called with correct parameters
    mock_s3_client.get_object.assert_called_once_with(
        Bucket="test-bucket",
        Key="test_file.txt"
    )


def test_s3_upload_file(test_client, api_key, mock_s3_client):
    """
    Test uploading a file to S3
    """
    # Get an auth token first
    auth_response = test_client.post(
        "/auth/token",
        headers={"X-API-Key": api_key}
    )
    token = auth_response.json()["access_token"]
    
    # Call the upload endpoint
    file_content = b"test file content to upload"
    response = test_client.post(
        "/s3/buckets/test-bucket/file?object_key=test_upload.txt",  # Changed URL
        headers={"Authorization": f"Bearer {token}"},
        files={"file": ("test_upload.txt", file_content)}
    )
    
    # Check the response
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert data["bucket"] == "test-bucket"
    assert data["file_path"] == "test_upload.txt"
    
    # This checks that any upload_fileobj method was called
    # The exact IO comparison is more complex with FastAPI's UploadFile
    mock_s3_client.upload_fileobj.assert_called_once()


def test_s3_delete_file(test_client, api_key, mock_s3_client):
    """
    Test deleting a file from S3
    """
    # Get an auth token first
    auth_response = test_client.post(
        "/auth/token",
        headers={"X-API-Key": api_key}
    )
    token = auth_response.json()["access_token"]
    
    # Call the delete endpoint
    response = test_client.delete(
        "/s3/buckets/test-bucket/file?object_key=test_file.txt",  # Changed URL
        headers={"Authorization": f"Bearer {token}"}
    )
    
    # Check the response
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert data["bucket"] == "test-bucket"
    assert data["file_path"] == "test_file.txt"
    
    # Verify the S3 client was called with correct parameters
    mock_s3_client.delete_object.assert_called_once_with(
        Bucket="test-bucket",
        Key="test_file.txt"
    )


def test_s3_error_handling(test_client, api_key, mock_s3_client):
    """
    Test error handling for S3 operations
    """
    # Get an auth token first
    auth_response = test_client.post(
        "/auth/token",
        headers={"X-API-Key": api_key}
    )
    token = auth_response.json()["access_token"]
    
    # Configure mock to raise a ClientError with NoSuchKey
    error_response = {
        "Error": {
            "Code": "NoSuchKey",
            "Message": "The specified key does not exist."
        }
    }
    mock_s3_client.get_object.side_effect = ClientError(error_response, "GetObject")
    
    # Call the download endpoint
    response = test_client.get(
        "/s3/buckets/test-bucket/file?object_key=missing_file.txt",  # Changed URL
        headers={"Authorization": f"Bearer {token}"}
    )
    
    # Check the response
    assert response.status_code == 404
    assert "File not found" in response.json()["detail"] 